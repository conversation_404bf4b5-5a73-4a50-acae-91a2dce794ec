import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { useEffect } from "react";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";

const queryClient = new QueryClient();

const App = () => {

  // Ensure page always starts from top on load and refresh
  useEffect(() => {
    // Force scroll to top on mount and refresh
    const scrollToTop = () => {
      window.scrollTo(0, 0);
    };
    
    // Scroll immediately
    scrollToTop();
    
    // Also scroll when page becomes visible (handles refresh)
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        setTimeout(scrollToTop, 0);
      }
    };
    
    // Handle page load events
    const handleLoad = () => {
      setTimeout(scrollToTop, 0);
    };
    
    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('load', handleLoad);
    
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('load', handleLoad);
    };
  }, []);

  return (
    <QueryClientProvider client={queryClient}>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            <Route path="/" element={<Index />} />
            {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </QueryClientProvider>
  );
};

export default App;
